"""
HTML生成器 - 负责HTML文档创建、表格合并和最终输出
"""

import os
from typing import List, Dict, Any, Optional
from bs4 import BeautifulSoup

from config import OUTPUT_HTML_FILE, HTML_TEMPLATE


class HTMLGenerator:
    """HTML生成器，负责创建和管理HTML文档"""

    def __init__(self, output_file: str = OUTPUT_HTML_FILE):
        """
        初始化HTML生成器

        Args:
            output_file: 输出文件路径
        """
        self.output_file = output_file
        self.content_blocks = []  # 存储页面内容块
        self.current_table = None  # 当前正在构建的表格
        self.page_counter = 0

    def start_new_document(self) -> None:
        """开始新的HTML文档"""
        self.content_blocks = []
        self.current_table = None
        self.page_counter = 0

    def add_page_content(
        self, page_num: int, page_html: str, is_continuation: bool = False
    ) -> None:
        """
        添加页面内容，支持续表合并

        Args:
            page_num: 页码
            page_html: 页面HTML内容
            is_continuation: 是否为续表
        """
        if not page_html or page_html.strip() == "":
            return

        # 清理HTML内容
        cleaned_html = self._clean_html(page_html)
        if not cleaned_html:
            return

        # 检查是否包含表格
        has_table = "<table" in cleaned_html.lower()

        if has_table and is_continuation and self.current_table:
            # 如果是续表，特殊处理：只合并第一个表格，其他内容正常添加
            print(f"处理续表合并...")
            self._merge_continuation_table_only(cleaned_html)
        else:
            # 对于新内容，先完成当前表格（如果有），然后添加新内容
            if self.current_table:
                self._finalize_current_table()

            # 直接添加完整内容，保持原始顺序
            print(f"添加页面内容...")
            self.content_blocks.append(cleaned_html)

            # 如果包含表格，跟踪最后一个表格以便续表合并
            if has_table:
                self._track_last_table(cleaned_html)

    def _separate_table_and_text(self, html_content: str) -> tuple[str, str]:
        """
        分离HTML内容中的表格和非表格内容

        Args:
            html_content: 混合HTML内容

        Returns:
            (表格内容, 非表格内容)
        """
        if not html_content:
            return "", ""

        from bs4 import BeautifulSoup

        soup = BeautifulSoup(html_content, "html.parser")

        # 查找所有表格
        tables = soup.find_all("table")

        if not tables:
            # 没有表格，全部是文本内容
            return "", html_content

        # 提取表格内容
        table_content = ""
        for table in tables:
            table_content += str(table)

        # 创建一个新的soup副本来处理非表格内容
        text_soup = BeautifulSoup(html_content, "html.parser")

        # 移除所有表格
        for table in text_soup.find_all("table"):
            table.decompose()

        # 获取剩余的非表格内容
        remaining_content = str(text_soup).strip()

        # 清理空的HTML标签
        if remaining_content in ["<html></html>", "<html><body></body></html>", ""]:
            remaining_content = ""

        return table_content, remaining_content

    def _extract_table_rows(self, html_content: str) -> List[str]:
        """
        从HTML中提取表格行

        Args:
            html_content: HTML内容

        Returns:
            表格行列表
        """
        if not html_content:
            return []

        soup = BeautifulSoup(html_content, "html.parser")
        table = soup.find("table")

        if not table:
            return []

        rows = table.find_all("tr")
        return [str(row) for row in rows]

    def _track_last_table(self, content_html: str) -> None:
        """跟踪内容中的最后一个表格，用于续表合并"""
        # 提取最后一个表格
        from bs4 import BeautifulSoup

        soup = BeautifulSoup(content_html, "html.parser")
        tables = soup.find_all("table")

        if tables:
            # 跟踪最后一个表格
            last_table = tables[-1]
            self.current_table = {
                "html": str(last_table),
                "rows": self._extract_table_rows(str(last_table)),
                "content_block_index": len(self.content_blocks)
                - 1,  # 记录表格所在的content block
            }
            print(f"跟踪最后一个表格，包含 {len(self.current_table['rows'])} 行")

    def _merge_continuation_table(self, continuation_html: str) -> None:
        """合并续表到当前表格，同时处理非表格内容"""
        if not self.current_table:
            print("警告：没有当前表格可以合并续表")
            return

        # 分离续表的表格和非表格内容
        table_content, non_table_content = self._separate_table_and_text(
            continuation_html
        )

        # 先添加非表格内容
        if non_table_content:
            self.content_blocks.append(non_table_content)
            print(f"已添加续表页面的非表格内容")

        # 合并表格行
        if table_content:
            continuation_rows = self._extract_table_rows(table_content)
            if continuation_rows:
                self.current_table["rows"].extend(continuation_rows)

                # 更新原来的content block中的表格
                self._update_table_in_content_block()

                print(
                    f"已合并 {len(continuation_rows)} 行到当前表格，总行数: {len(self.current_table['rows'])}"
                )
            else:
                print("续表中没有找到表格行")
        else:
            print("续表中没有找到表格内容")

    def _merge_continuation_table_only(self, continuation_html: str) -> None:
        """只合并续表，其他内容正常添加"""
        if not self.current_table:
            print("警告：没有当前表格可以合并续表")
            # 如果没有当前表格，就直接添加内容
            self.content_blocks.append(continuation_html)
            return

        from bs4 import BeautifulSoup

        soup = BeautifulSoup(continuation_html, "html.parser")
        tables = soup.find_all("table")

        if not tables:
            # 没有表格，直接添加内容
            self.content_blocks.append(continuation_html)
            return

        # 处理第一个表格作为续表
        first_table = tables[0]
        continuation_rows = self._extract_table_rows(str(first_table))

        if continuation_rows:
            # 合并到当前表格
            self.current_table["rows"].extend(continuation_rows)
            self._update_table_in_content_block()
            print(
                f"已合并 {len(continuation_rows)} 行到当前表格，总行数: {len(self.current_table['rows'])}"
            )

        # 移除第一个表格，保留其他内容
        first_table.decompose()

        # 添加剩余内容（包括其他表格）
        remaining_content = str(soup).strip()
        if remaining_content and remaining_content not in [
            "<html></html>",
            "<html><body></body></html>",
            "",
        ]:
            self.content_blocks.append(remaining_content)
            print(f"已添加续表页面的其他内容")

    def _update_table_in_content_block(self) -> None:
        """更新content block中的表格内容"""
        if not self.current_table or "content_block_index" not in self.current_table:
            return

        block_index = self.current_table["content_block_index"]
        if block_index >= len(self.content_blocks):
            return

        # 重建完整的表格HTML
        new_table_html = self._rebuild_table_html(self.current_table["rows"])

        # 替换原content block中的表格
        from bs4 import BeautifulSoup

        original_content = self.content_blocks[block_index]
        soup = BeautifulSoup(original_content, "html.parser")

        # 找到并替换表格
        tables = soup.find_all("table")
        if tables:
            # 替换最后一个表格（我们跟踪的那个）
            last_table = tables[-1]
            new_table_soup = BeautifulSoup(new_table_html, "html.parser")
            last_table.replace_with(new_table_soup.find("table"))

            # 更新content block
            self.content_blocks[block_index] = str(soup)
            print(f"已更新content block {block_index}中的表格")

    def _finalize_current_table(self) -> None:
        """完成当前表格跟踪"""
        if not self.current_table:
            return

        print(f"已完成表格跟踪，包含 {len(self.current_table['rows'])} 行")
        self.current_table = None

    def _rebuild_table_html(self, rows: List[str]) -> str:
        """
        重建完整的表格HTML

        Args:
            rows: 表格行列表

        Returns:
            完整的表格HTML
        """
        if not rows:
            return ""

        table_html = "<table>\n"
        for row in rows:
            table_html += f"  {row}\n"
        table_html += "</table>"

        return table_html

    def _clean_html(self, html_content: str) -> str:
        """
        清理HTML内容

        Args:
            html_content: 原始HTML内容

        Returns:
            清理后的HTML内容
        """
        if not html_content:
            return ""

        # 移除多余的空白和换行
        html_content = html_content.strip()

        # 使用BeautifulSoup格式化HTML
        try:
            soup = BeautifulSoup(html_content, "html.parser")

            # 移除不必要的属性，但保留重要的表格属性
            for tag in soup.find_all():
                important_attrs = ["colspan", "rowspan", "class", "id"]
                attrs_to_remove = []

                for attr in tag.attrs:
                    if attr not in important_attrs:
                        attrs_to_remove.append(attr)

                for attr in attrs_to_remove:
                    del tag[attr]

            return str(soup)
        except Exception as e:
            print(f"HTML清理时出错: {str(e)}")
            return html_content

    def finalize_document(self) -> str:
        """
        完成文档并返回最终HTML

        Returns:
            完整的HTML文档
        """
        # 完成最后一个表格
        if self.current_table:
            self._finalize_current_table()

        # 合并所有内容块
        content = "\n".join(self.content_blocks)

        # 使用模板生成最终HTML
        final_html = HTML_TEMPLATE.format(content=content)

        return final_html

    def save_to_file(self, html_content: str = None) -> str:
        """
        保存HTML到文件

        Args:
            html_content: HTML内容（如果为None，则使用finalize_document()的结果）

        Returns:
            保存的文件路径
        """
        if html_content is None:
            html_content = self.finalize_document()

        with open(self.output_file, "w", encoding="utf-8") as f:
            f.write(html_content)

        return os.path.abspath(self.output_file)

    def get_current_content_preview(self) -> str:
        """
        获取当前内容的预览

        Returns:
            当前内容的HTML预览
        """
        preview_blocks = self.content_blocks.copy()

        # 如果有当前正在构建的表格，也包含在预览中
        if self.current_table:
            table_html = self._rebuild_table_html(self.current_table["rows"])
            if table_html:
                preview_blocks.append(table_html)

        content = "\n".join(preview_blocks)
        return HTML_TEMPLATE.format(content=content)
