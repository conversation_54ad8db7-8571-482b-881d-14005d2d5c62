"""
PDF转HTML工作流 - 重新梳理后的简化版本
使用LangGraph编排整个处理流程
"""

import os
import json
from datetime import datetime
from typing import Dict, Any, List, Optional, TypedDict, Tuple
from dataclasses import dataclass

from langgraph.graph import StateGraph, END
from langgraph.graph.message import add_messages
from langchain_core.messages import BaseMessage

from pdf_processor import PDFProcessor
from vlm_service import VLMContentRecognizer
from html_generator import HTMLGenerator


class WorkflowState(TypedDict):
    """工作流状态定义 - 简化版"""

    pdf_path: str
    current_page: int
    total_pages: int
    page_image: Optional[Any]  # PIL Image
    page_content_html: str  # 页面HTML内容
    content_type: str  # 内容类型
    is_continuation: bool  # 是否为续表
    html_generator: Optional[HTMLGenerator]
    error_message: str
    completed: bool
    page_range: Optional[Tuple[int, int]]  # 页面范围


@dataclass
class PDF2HTMLWorkflowClean:
    """PDF转HTML工作流类 - 简化版"""

    def __init__(self, pdf_path: str, output_file: str = "output.html"):
        """
        初始化工作流

        Args:
            pdf_path: PDF文件路径
            output_file: 输出HTML文件路径
        """
        self.pdf_path = pdf_path
        self.output_file = output_file

        # 初始化各个组件
        self.pdf_processor = PDFProcessor(pdf_path)
        self.vlm_recognizer = VLMContentRecognizer()
        self.html_generator = HTMLGenerator(output_file)

        # 初始化VLM响应存储
        self.vlm_responses = []
        self._setup_vlm_response_storage()

        # 构建工作流图
        self.workflow = self._build_workflow()

    def _build_workflow(self) -> StateGraph:
        """构建LangGraph工作流 - 简化版"""

        # 创建状态图
        workflow = StateGraph(WorkflowState)

        # 添加节点 - 简化流程
        workflow.add_node("initialize", self._initialize_processing)
        workflow.add_node("extract_page", self._extract_page_image)
        workflow.add_node(
            "recognize_content", self._recognize_content
        )  # 一步完成识别和分析
        workflow.add_node("process_content", self._process_page_content)
        workflow.add_node("next_page", self._move_to_next_page)
        workflow.add_node("finalize", self._finalize_document)

        # 设置入口点
        workflow.set_entry_point("initialize")

        # 添加边 - 简化流程
        workflow.add_edge("initialize", "extract_page")
        workflow.add_edge("extract_page", "recognize_content")
        workflow.add_edge("recognize_content", "process_content")

        # 条件边：决定是否继续处理下一页
        workflow.add_conditional_edges(
            "process_content",
            self._should_continue,
            {"continue": "next_page", "finish": "finalize"},
        )

        workflow.add_edge("next_page", "extract_page")
        workflow.add_edge("finalize", END)

        return workflow.compile()

    def _setup_vlm_response_storage(self) -> None:
        """设置VLM响应存储"""
        # 创建响应存储目录
        self.vlm_response_dir = "vlm_responses"
        if not os.path.exists(self.vlm_response_dir):
            os.makedirs(self.vlm_response_dir)

        # 生成时间戳作为文件名前缀
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        pdf_name = os.path.splitext(os.path.basename(self.pdf_path))[0]
        self.response_file_prefix = f"{timestamp}_{pdf_name}"

    def _save_vlm_response(
        self, page_num: int, content_type: str, is_continuation: bool, html_content: str
    ) -> None:
        """保存VLM原始响应"""
        response_data = {
            "timestamp": datetime.now().isoformat(),
            "page_num": page_num,
            "content_type": content_type,
            "is_continuation": is_continuation,
            "html_content": html_content,
            "html_length": len(html_content),
        }

        # 添加到响应列表
        self.vlm_responses.append(response_data)

        # 保存单页响应到独立文件
        page_file = os.path.join(
            self.vlm_response_dir,
            f"{self.response_file_prefix}_page_{page_num:02d}.json",
        )
        with open(page_file, "w", encoding="utf-8") as f:
            json.dump(response_data, f, ensure_ascii=False, indent=2)

        # 保存单页响应到可读文本文件
        txt_file = os.path.join(
            self.vlm_response_dir,
            f"{self.response_file_prefix}_page_{page_num:02d}.txt",
        )
        with open(txt_file, "w", encoding="utf-8") as f:
            f.write(f"VLM响应 - 第 {page_num} 页\n")
            f.write("=" * 50 + "\n")
            f.write(f"时间戳: {response_data['timestamp']}\n")
            f.write(f"内容类型: {content_type}\n")
            f.write(f"是否续表: {'是' if is_continuation else '否'}\n")
            f.write(f"HTML长度: {len(html_content)} 字符\n")
            f.write("\nHTML内容:\n")
            f.write("-" * 30 + "\n")
            f.write(html_content)
            f.write("\n" + "-" * 30 + "\n")

        print(f"✅ VLM响应已保存: {page_file}")

    def _save_summary_vlm_responses(self) -> None:
        """保存汇总的VLM响应"""
        if not self.vlm_responses:
            return

        # 保存汇总JSON文件
        summary_file = os.path.join(
            self.vlm_response_dir, f"{self.response_file_prefix}_summary.json"
        )
        summary_data = {
            "pdf_file": self.pdf_path,
            "total_pages": len(self.vlm_responses),
            "processing_time": datetime.now().isoformat(),
            "responses": self.vlm_responses,
        }

        with open(summary_file, "w", encoding="utf-8") as f:
            json.dump(summary_data, f, ensure_ascii=False, indent=2)

        # 保存汇总文本文件
        summary_txt = os.path.join(
            self.vlm_response_dir, f"{self.response_file_prefix}_summary.txt"
        )

        with open(summary_txt, "w", encoding="utf-8") as f:
            f.write(f"VLM响应汇总报告\n")
            f.write("=" * 60 + "\n")
            f.write(f"PDF文件: {self.pdf_path}\n")
            f.write(f"处理时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"总页数: {len(self.vlm_responses)}\n")
            f.write("\n")

            for response in self.vlm_responses:
                f.write(f"第 {response['page_num']} 页:\n")
                f.write(f"  内容类型: {response['content_type']}\n")
                f.write(
                    f"  是否续表: {'是' if response['is_continuation'] else '否'}\n"
                )
                f.write(f"  HTML长度: {response['html_length']} 字符\n")
                f.write(f"  处理时间: {response['timestamp']}\n")
                f.write("\n")

        print(f"📋 VLM响应汇总已保存: {summary_file}")
        print(f"📁 VLM响应目录: {os.path.abspath(self.vlm_response_dir)}")

    def _initialize_processing(self, state: WorkflowState) -> WorkflowState:
        """初始化处理流程"""
        print("开始PDF转HTML处理...")

        total_pages = self.pdf_processor.get_page_count()
        print(f"PDF总页数: {total_pages}")

        # 初始化HTML生成器
        self.html_generator.start_new_document()

        return {
            **state,
            "pdf_path": self.pdf_path,
            "current_page": 0,
            "total_pages": total_pages,
            "page_content_html": "",
            "content_type": "",
            "is_continuation": False,
            "html_generator": self.html_generator,
            "completed": False,
            "error_message": "",
        }

    def _extract_page_image(self, state: WorkflowState) -> WorkflowState:
        """提取页面图像"""
        current_page = state["current_page"]
        print(f"处理第 {current_page + 1} 页...")

        try:
            # 转换页面为图像
            page_image = self.pdf_processor.convert_page_to_image(current_page)

            return {**state, "page_image": page_image, "error_message": ""}
        except Exception as e:
            error_msg = f"提取第{current_page + 1}页图像时出错: {str(e)}"
            print(error_msg)
            return {**state, "page_image": None, "error_message": error_msg}

    def _recognize_content(self, state: WorkflowState) -> WorkflowState:
        """识别页面内容（一步完成识别、分析和HTML转换）"""
        page_image = state["page_image"]
        current_page = state["current_page"]

        if not page_image:
            return {
                **state,
                "page_content_html": "",
                "content_type": "",
                "is_continuation": False,
            }

        max_retries = 3
        retry_delay = 10

        for attempt in range(max_retries):
            try:
                print(
                    f"识别第 {current_page + 1} 页的内容... (尝试 {attempt+1}/{max_retries})"
                )

                # 使用VLM一步完成识别和分析
                content_type, is_continuation, page_html = (
                    self.vlm_recognizer.analyze_page_content(page_image)
                )

                # 保存VLM原始响应
                self._save_vlm_response(
                    current_page + 1, content_type, is_continuation, page_html
                )

                print(f"内容类型: {content_type}")
                print(f"是否续表: {'是' if is_continuation else '否'}")
                print(f"HTML长度: {len(page_html)} 字符")

                return {
                    **state,
                    "page_content_html": page_html,
                    "content_type": content_type,
                    "is_continuation": is_continuation,
                    "error_message": "",
                }
            except Exception as e:
                error_msg = f"识别第{current_page + 1}页内容时出错: {str(e)}"
                print(error_msg)

                if attempt < max_retries - 1:
                    print(f"等待 {retry_delay} 秒后重试...")
                    import time

                    time.sleep(retry_delay)
                    retry_delay *= 2
                else:
                    print(f"所有重试都失败了，跳过此页")
                    return {
                        **state,
                        "page_content_html": "",
                        "content_type": "",
                        "is_continuation": False,
                        "error_message": error_msg,
                    }

        return {
            **state,
            "page_content_html": "",
            "content_type": "",
            "is_continuation": False,
            "error_message": "未知错误",
        }

    def _process_page_content(self, state: WorkflowState) -> WorkflowState:
        """处理页面内容"""
        page_html = state["page_content_html"]
        content_type = state["content_type"]
        is_continuation = state["is_continuation"]
        current_page = state["current_page"]
        html_generator = state["html_generator"]

        try:
            print(f"处理第 {current_page + 1} 页的内容...")

            if page_html:
                # 添加页面内容到HTML生成器，传递续表信息
                html_generator.add_page_content(
                    current_page, page_html, is_continuation
                )
                print(
                    f"已添加第 {current_page + 1} 页的内容 (类型: {content_type}, 续表: {'是' if is_continuation else '否'})"
                )
            else:
                print(f"第 {current_page + 1} 页没有内容")

            return {**state, "error_message": ""}
        except Exception as e:
            error_msg = f"处理第{current_page + 1}页内容时出错: {str(e)}"
            print(error_msg)
            return {**state, "error_message": error_msg}

    def _move_to_next_page(self, state: WorkflowState) -> WorkflowState:
        """移动到下一页"""
        current_page = state["current_page"]
        next_page = current_page + 1

        print(f"移动到第 {next_page + 1} 页...")

        return {
            **state,
            "current_page": next_page,
            "page_image": None,
            "page_content_html": "",
            "content_type": "",
            "is_continuation": False,
        }

    def _finalize_document(self, state: WorkflowState) -> WorkflowState:
        """完成文档生成"""
        html_generator = state["html_generator"]

        try:
            print("生成最终HTML文档...")

            # 生成最终HTML
            final_html = html_generator.finalize_document()

            # 保存到文件
            output_path = html_generator.save_to_file(final_html)

            # 保存汇总的VLM响应
            self._save_summary_vlm_responses()

            print(f"HTML文档已保存到: {output_path}")

            return {**state, "completed": True, "error_message": ""}
        except Exception as e:
            error_msg = f"生成最终文档时出错: {str(e)}"
            print(error_msg)
            return {**state, "completed": False, "error_message": error_msg}

    def _should_continue(self, state: WorkflowState) -> str:
        """判断是否应该继续处理下一页"""
        current_page = state["current_page"]
        total_pages = state["total_pages"]

        # 检查是否有页面范围限制
        if "page_range" in state and state["page_range"]:
            start_page, end_page = state["page_range"]
            print(
                f"检查是否继续: 当前页={current_page+1}, 范围=[{start_page+1}, {end_page}]"
            )

            if current_page + 1 >= end_page:
                print(f"已达到指定页面范围的末尾，停止处理")
                return "finish"
        elif current_page + 1 >= total_pages:
            print(f"已达到PDF文件的末尾，停止处理")
            return "finish"

        print(f"继续处理下一页")
        return "continue"

    def run(self, page_range: Optional[Tuple[int, int]] = None) -> Dict[str, Any]:
        """运行工作流"""
        print(f"开始处理PDF文件: {self.pdf_path}")

        # 获取总页数
        total_pages = self.pdf_processor.get_page_count()

        # 确定要处理的页面范围
        if page_range:
            start_page, end_page = page_range
            start_page = max(0, start_page)
            end_page = min(total_pages, end_page)
            print(f"处理页面范围: {start_page + 1} 到 {end_page}")
        else:
            start_page, end_page = 0, total_pages
            print(f"处理所有页面: 1 到 {total_pages}")

        # 初始状态
        initial_state = WorkflowState(
            pdf_path=self.pdf_path,
            current_page=start_page,
            total_pages=total_pages,
            page_image=None,
            page_content_html="",
            content_type="",
            is_continuation=False,
            html_generator=None,
            error_message="",
            completed=False,
            page_range=(start_page, end_page),
        )

        # 运行工作流
        try:
            config = {"recursion_limit": 200}
            final_state = self.workflow.invoke(initial_state, config=config)

            processed_pages = end_page - start_page

            if final_state["completed"]:
                print("PDF转HTML处理完成！")
                return {
                    "success": True,
                    "output_file": self.output_file,
                    "total_pages": total_pages,
                    "processed_pages": processed_pages,
                    "error_message": "",
                }
            else:
                print(f"处理失败: {final_state['error_message']}")
                return {
                    "success": False,
                    "output_file": "",
                    "total_pages": total_pages,
                    "processed_pages": processed_pages,
                    "error_message": final_state["error_message"],
                }
        except Exception as e:
            error_msg = f"工作流执行出错: {str(e)}"
            print(error_msg)
            import traceback

            traceback.print_exc()
            return {
                "success": False,
                "output_file": "",
                "total_pages": total_pages,
                "processed_pages": 0,
                "error_message": error_msg,
            }
