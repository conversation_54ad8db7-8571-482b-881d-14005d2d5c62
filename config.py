"""
配置文件
"""

# VLM API 配置
QWEN_VL_API_KEY = "sk-6119bb6e5a95440598ef80c25a40dc8e"
QWEN_VL_BASE_URL = "https://dashscope.aliyuncs.com/compatible-mode/v1"
QWEN_VL_MODEL = "qwen-vl-max"

# PDF 处理配置
PDF_DPI = 200  # PDF转图像的DPI
IMAGE_FORMAT = "PNG"  # 图像格式

# HTML 输出配置
OUTPUT_HTML_FILE = "output.html"
HTML_TEMPLATE = """<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>PDF转HTML结果</title>
    <style>
        body {{
            font-family: Arial, sans-serif;
            margin: 20px;
            line-height: 1.6;
            color: #333;
        }}
        table {{
            border-collapse: collapse;
            width: 100%;
            margin: 20px 0;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }}
        th, td {{
            border: 1px solid #ddd;
            padding: 12px 8px;
            text-align: left;
            vertical-align: top;
        }}
        th {{
            background-color: #f8f9fa;
            font-weight: bold;
            color: #495057;
        }}
        .page-break {{
            page-break-before: always;
        }}
        .page-marker {{
            color: #6c757d;
            font-size: 0.9em;
            margin: 10px 0;
            border-bottom: 1px solid #e9ecef;
            padding-bottom: 5px;
        }}
        p {{
            margin: 10px 0;
            text-align: justify;
        }}
        h1, h2, h3, h4, h5, h6 {{
            color: #2c3e50;
            margin: 20px 0 10px 0;
        }}
        ul, ol {{
            margin: 10px 0;
            padding-left: 30px;
        }}
        li {{
            margin: 5px 0;
        }}
    </style>
</head>
<body>
{content}
</body>
</html>"""

# 表格检测配置
# 统一的内容识别提示词
PARSE_PDF_PROMPT = """
你是一个极其严谨、精准的PDF内容结构化分析专家。你的任务是**绝对忠实**地复刻PDF页面图像中的内容与结构，并将其转换为高质量的HTML。

在开始任何任务之前，你必须牢记并严格遵守以下【黄金三大原则】，这是你所有行为的最高准则：

### 黄金三大原则

1.  **绝对忠实原则 (Absolute Fidelity)**
    * **禁止任何形式的创造、补充、或修改**。你看到的每一个文字、数字、符号都必须原样出现在HTML中。
    * **禁止对内容进行“总结”或“优化”**。例如，如果原文是“前10名股东持股情况”，你就不能自行添加`<caption>`标签并写成“报告期末普通股股东总数及前10名股东持股情况”。
    * 内容的**顺序**、**数量**和**样式（如粗体）**必须完全复刻。

2.  **清晰边界原则 (Clear Boundaries)**
    * **一个视觉上独立的块，就是一个独立的HTML元素**。
    * 如果页面顶部有一个`<h3>`标题，其下方有一个`<p>`段落，再下方是一个`<table>`，它们在视觉上有间距，那么在HTML中它们必须是三个独立的、兄弟级的标签，**绝对禁止**将标题或段落错误地塞入`<table>`内部。
    * 如果页面上有两个视觉上独立的表格，即使它们紧挨着，也必须生成两个独立的`<table>`标签。

3.  **结构优先原则 (Structure First)**
    * HTML的结构必须由内容的**视觉布局**唯一确定。
    * 你必须先完整理解整个页面或表格的宏观结构，再动手生成HTML标签。禁止“走一步看一步”的生成方式，因为那会导致结构错乱。

---

### 核心任务：将页面完整转换为HTML

#### **第一部分：页面全局内容解析**

遵循【清晰边界原则】，从上到下识别页面中的每一个独立视觉块，并转换为对应的HTML标签：
* 独立的文本标题 -> `<h1>` - `<h6>`
* 独立的段落/说明文字 -> `<p>`
* 独立的表格 -> `<table>` (使用下面的“四步表格解析法”进行处理)

---

#### **第二部分：【全新】四步表格解析法**

为了根除复杂表格（特别是嵌套表头）的解析错误，你必须放弃之前的思考方式，严格执行以下全新的、基于“虚拟网格”的四步法：

**第一步：构建虚拟网格 (Build a Virtual Grid)**
1.  **确定列数 (N)**：首先审视表格的**数据区**（非表头部分），找到并确定最细粒度的列数。这是整个表格的基准总列数 `N`。
2.  **确定行数 (M)**：计算整个表格（包括所有表头行和数据行）在视觉上共有多少行 `M`。
3.  **创建网格**：在你的“脑海”中，创建一个 `M` 行 `N` 列的虚拟坐标网格。例如，一个3行4列的表格就有一个从 `[0,0]` 到 `[2,3]` 的坐标系。

**第二步：单元格填充与坐标映射 (Fill the Grid & Map Coordinates)**
1.  现在，将PDF中看到的每一个视觉单元格，“贴”到这个虚拟网格上，并记录它占据的所有坐标。
2.  **示例**：
    * 一个简单的单元格，只会占据一个坐标，如 `[1,1]`。
    * 一个跨2列的单元格，会占据两个坐标，如 `[0,2]` 和 `[0,3]`。
    * 一个跨2行的单元格，会占据两个坐标，如 `[0,0]` 和 `[1,0]`。
    * 一个复杂的、跨2行2列的单元格，会占据四个坐标：`[0,0]`, `[0,1]`, `[1,0]`, `[1,1]`。

**第三步：从坐标计算Spans (Calculate Spans from Coordinates)**
1.  **只有在完成坐标映射后**，才能开始计算 `rowspan` 和 `colspan`。
2.  **`colspan` 计算**：一个单元格占据了多少个**不同的列坐标**，`colspan` 就是多少。
3.  **`rowspan` 计算**：一个单元格占据了多少个**不同的行坐标**，`rowspan` 就是多少。
4.  这个方法将计算过程与布局分析完全分离，从根本上避免了逻辑混乱。

**第四步：生成HTML并严格排序 (Generate HTML with Strict Ordering)**
1.  现在，根据每个单元格的内容及其计算出的 `rowspan` 和 `colspan` 属性，生成HTML代码。
2.  **在生成`<table>`时，必须遵守W3C的官方排序**：
    * 如果存在表格标题（通常是表格正上方或下方**紧邻**的、**独立**的文本行），应使用`<caption>`标签，并且它**必须**是`<table>`元素的**第一个子元素**。
    * 然后是 `<thead>`，包含所有表头行。
    * 然后是 `<tbody>`，包含所有数据行。
    * 禁止将`<caption>`放在`<thead>`或`<tbody>`之后。

---

### **返回格式**

请严格按照以下格式返回你的分析结果：

内容类型：[纯文本/包含表格/混合内容]
HTML：
[这里是遵循以上所有原则和步骤生成的、绝对忠实于原文的、结构正确的HTML代码，不要包含`<html>`, `<head>`, `<body>`标签]
"""
TABLE_JUDGE_PROMPT = """
# Prompt: 续表判断专家

你是一个专业的文档页面视觉分析专家，专门负责判断页面顶部的表格是否为从上一页延续而来的**续表**。

你的唯一任务是分析单张页面图像，并根据严格的视觉特征排除法，判定页面顶部的表格是否为一个续表。你必须清楚地意识到，你唯一的输入是当前页面的图像，无法获取上一页或下一页的任何信息。

---

### 判定逻辑

由于你无法看到上一页，必须通过以下“排除法”逻辑进行严密推理：

#### 第一步：定位候选对象
检查页面内容区域的**第一个可见元素**是否为一个表格。
- 如果不是表格，则本页没有续表。判定为**否**。
- 如果它是一个表格，则它是一个“续表候选者”，进入下一步进行严格审查。

#### 第二步：新表格的决定性特征测试（核心排除逻辑）
对这个“候选表格”进行检查，看它是否具备以下**任何一条**“新表格”的决定性视觉特征。只要满足**任意一条**，即可判定为**否**。

* **特征A：存在前导视觉标题**
    * 检查表格的正上方、紧邻的位置，是否存在一段**独立的文本**？判断这段文本是否为标题，需满足以下视觉特征：
        * **字号更大**：其字体尺寸明显大于表格内正文的字号。
        * **样式突出**：其字体可能为**粗体**。
        * **位置独立**：它位于表格的边框之外，并有一定间距，不属于表格的一部分。
    * 如果**存在**这样的视觉标题，则为新表格。判定为**否**。

* **特征B：存在视觉表头 (Header)**
    * 检查表格的**第一行**，看它的视觉样式和内容是否符合表头特征：
        * **视觉样式**：第一行文字是否**加粗**、**居中**、或单元格是否有不同于数据行的**背景色**？
        * **文本内容**：第一行包含的是否是**类别名称**（如“姓名”、“日期”、“金额”），而非具体数据？
    * 如果**存在**这样的视觉表头，则为新表格。判定为**否**。

* **特征C：存在内嵌的表格标题或单位说明**
    * 检查表格的**边框内部**或**紧邻其上下方**的位置，是否存在用于解释整个表格的元信息文本？
    * 例如，常见的“**单位：元**”、“**数据来源：**”等通常位于表格的右上角。或一个紧贴在表格上方的、字号与正文相近的标题。
    * 如果**存在**这类内嵌信息，则为新表格。判定为**否**。

#### 第三步：最终裁决
- 如果一个位于页面顶部的表格，**同时通过了**上述所有的排除测试（即：它**没有**前导视觉标题，第一行**不是**视觉表头，也**没有**内嵌的标题或单位说明），那么我们就可以通过排除法，非常有信心地判定它是一个**续表**。
- 在这种情况下，判定为**是**。

---

### 返回格式

请严格按照以下格式返回你的最终判断结果，不包含任何多余的解释或说明：
# UNIFIED_CONTENT_PROMPT = """
# 你是一个专业的PDF内容识别与结构化分析专家。你的任务是分析单张PDF页面图像，并完成以下任务。你必须清楚地意识到，你唯一的输入是当前页面的图像，无法获取上一页或下一页的任何信息。

# 1.  **识别**图像中的所有内容。
# 2.  **分析**并**判定**页面顶部的表格是否为一个从上一页延续而来的**续表**。
# 3.  **转换**所有内容为HTML格式。

# 请严格按照以下格式返回结果：

# 内容类型：[纯文本/包含表格/混合内容]
# 是否续表：[是/否]
# HTML：
# [完整的HTML代码]

# ---

# ### HTML转换要求
# - 段落文本使用`<p>`标签。
# - 标题应根据其视觉层级（字号、加粗等）转换为`<h1>`到`<h6>`标签。
# - 表格使用`<table>`、`<tr>`、`<td>`标签。若识别出表头，则使用`<th>`。若识别出表格标题/单位，则使用`<caption>`。
# - 正确处理单元格合并，使用`colspan`和`rowspan`属性。
# - 强调文本（如加粗、斜体）使用`<strong>`、`<em>`标签。
# - **不要**包含`<html>`、`<head>`和`<body>`标签。
# - **忽略**页面中任何的页眉（Header）和页脚（Footer）信息， 页眉一般位于页面顶部，有一条横线，页脚一般只有页数信息。

# ---

# ### 续表判断逻辑（纯视觉特征 + 单页上下文）

# 由于你无法看到上一页，必须通过以下“排除法”逻辑进行严密推理：

# **第一步：定位候选对象**
# 检查页面内容区域的**第一个可见元素**是否为一个表格。
# * 如果不是表格，则本页没有续表。判定为**否**。
# * 如果是表格，则它是一个“续表候选者”，进入下一步进行严格审查。

# **第二步：新表格的决定性特征测试（核心排除逻辑）**
# 对这个“候选表格”进行检查，看它是否具备以下**任何一条**“新表格”的决定性视觉特征。只要满足**任意一条**，即可判定为**否**。

# * **特征A：存在前导视觉标题**
#     * 检查表格的正上方、紧邻的位置，是否存在一段**独立的文本**？判断这段文本是否为标题，需满足以下视觉特征：
#         * **字号更大**：其字体尺寸明显大于表格内正文的字号。
#         * **样式突出**：其字体可能为**粗体**。
#         * **位置独立**：它位于表格的边框之外，并有一定间距，不属于表格的一部分。
#     * 如果**存在**这样的视觉标题，则为新表格。判定为**否**。

# * **特征B：存在视觉表头 (Header)**
#     * 检查表格的**第一行**，看它的视觉样式和内容是否符合表头特征：
#         * **视觉样式**：第一行文字是否**加粗**、**居中**、或单元格是否有不同于数据行的**背景色**？
#         * **文本内容**：第一行包含的是否是**类别名称**（如“姓名”、“日期”、“金额”），而非具体数据？
#     * 如果**存在**这样的视觉表头，则为新表格。判定为**否**。

# * **特征C：存在内嵌的表格标题或单位说明**
#     * 检查表格的**边框内部**或**紧邻其上下方**的位置，是否存在用于解释整个表格的元信息文本？
#     * 例如，常见的“**单位：元**”、“**数据来源：**”等通常位于表格的右上角。或一个紧贴在表格上方的、字号与正文相近的标题。
#     * 如果**存在**这类内嵌信息，则为新表格。判定为**否**。

# **第三步：最终裁决**
# * 如果一个位于页面顶部的表格，**同时通过了**上述所有的排除测试（即：它**没有**前导视觉标题，第一行**不是**视觉表头，也**没有**内嵌的标题或单位说明），那么我们就可以通过排除法，非常有信心地判定它是一个**续表**。
# * 在这种情况下，判定为**是**。
# """
