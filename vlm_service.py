"""
VLM表格识别服务 - 使用通义千问VL模型识别表格并转换为HTML
"""

import os
import json
import re
from typing import List, Dict, Any, Optional, Tuple
import time

import requests
from PIL import Image

from config import (
    QWEN_VL_API_KEY,
    QWEN_VL_BASE_URL,
    QWEN_VL_MODEL,
    UNIFIED_CONTENT_PROMPT,
)


class VLMContentRecognizer:
    """VLM内容识别器，使用通义千问VL模型识别PDF页面的所有内容（文本和表格）"""

    def __init__(
        self,
        api_key: str = QWEN_VL_API_KEY,
        base_url: str = QWEN_VL_BASE_URL,
        model: str = QWEN_VL_MODEL,
    ):
        """
        初始化VLM表格识别器

        Args:
            api_key: API密钥
            base_url: API基础URL
            model: 模型名称
        """
        self.api_key = api_key
        self.base_url = base_url
        self.model = model
        self._validate_credentials()

    def _validate_credentials(self) -> None:
        """验证API凭证是否有效"""
        if not self.api_key or not self.base_url:
            raise ValueError("API密钥和基础URL不能为空")

    def analyze_page_content(self, image: Image.Image) -> Tuple[str, bool, str]:
        """
        统一分析图像中的内容类型并转换为HTML

        这个方法一次性完成：
        1. 识别所有内容（文本、表格等）
        2. 判断内容类型
        3. 判断是否为续表
        4. 转换为HTML格式

        Args:
            image: PIL图像对象

        Returns:
            (内容类型, 是否为续表, HTML内容)
        """
        # 将图像转换为base64
        image_base64 = self._image_to_base64(image)

        # 调用API - 只调用一次
        response = self._call_api(image_base64, UNIFIED_CONTENT_PROMPT)

        # 提取结果
        content = self._extract_text_from_response(response)

        # 解析结果
        content_type = "混合内容"  # 默认值
        is_continuation = False
        html = ""

        # 尝试从响应中提取信息
        type_match = re.search(r"内容类型：\s*([^\n]+)", content)
        continuation_match = re.search(r"是否续表：\s*([^\n]+)", content)
        html_match = re.search(r"HTML：\s*(.+)", content, re.DOTALL)

        if type_match:
            content_type = type_match.group(1).strip()

        if continuation_match:
            is_continuation = "是" in continuation_match.group(1).strip()

        if html_match:
            html = html_match.group(1).strip()
            # 处理可能的```html代码块包装
            html = self._clean_html_code_block(html)

        return content_type, is_continuation, html

    def _clean_html_code_block(self, html_content: str) -> str:
        """
        清理HTML内容中的代码块包装

        Args:
            html_content: 可能包含```html包装的HTML内容

        Returns:
            清理后的HTML内容
        """
        if not html_content:
            return ""

        html_content = html_content.strip()

        # 处理```html代码块包装
        if html_content.startswith("```html"):
            # 找到HTML代码块的开始和结束
            start_marker = "```html"
            end_marker = "```"

            start_idx = html_content.find(start_marker)
            if start_idx != -1:
                start_idx += len(start_marker)
                end_idx = html_content.find(end_marker, start_idx)
                if end_idx != -1:
                    html_content = html_content[start_idx:end_idx].strip()
                else:
                    html_content = html_content[start_idx:].strip()

        # 处理其他可能的代码块包装
        elif html_content.startswith("```") and html_content.endswith("```"):
            # 移除开头和结尾的```
            lines = html_content.split("\n")
            if len(lines) > 2:
                html_content = "\n".join(lines[1:-1]).strip()

        # 如果返回的是完整的HTML文档，提取body内容
        if html_content.startswith("<!DOCTYPE html>") or html_content.startswith(
            "<html"
        ):
            from bs4 import BeautifulSoup

            soup = BeautifulSoup(html_content, "html.parser")
            body = soup.find("body")
            if body:
                # 提取body内的所有内容
                return str(body).replace("<body>", "").replace("</body>", "").strip()

        return html_content

    def _image_to_base64(self, image: Image.Image, format: str = "PNG") -> str:
        """
        将PIL图像转换为base64编码

        Args:
            image: PIL图像对象
            format: 图像格式

        Returns:
            base64编码的图像字符串
        """
        import io
        import base64

        buffered = io.BytesIO()
        image.save(buffered, format=format)
        return base64.b64encode(buffered.getvalue()).decode("utf-8")

    def _call_api(self, image_base64: str, prompt: str) -> Dict[Any, Any]:
        """
        调用通义千问VL API

        Args:
            image_base64: base64编码的图像
            prompt: 提示词

        Returns:
            API响应
        """
        # 检查图像大小
        image_size_mb = len(image_base64) * 3 / 4 / 1024 / 1024  # base64解码后的大小
        print(f"图像大小: {image_size_mb:.2f} MB")

        if image_size_mb > 10:  # 如果图像超过10MB，可能会导致问题
            print("警告: 图像过大，可能导致API调用失败")

        headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json",
        }

        payload = {
            "model": self.model,
            "messages": [
                {
                    "role": "user",
                    "content": [
                        {"type": "text", "text": prompt},
                        {
                            "type": "image_url",
                            "image_url": {
                                "url": f"data:image/png;base64,{image_base64}"
                            },
                        },
                    ],
                }
            ],
            "max_tokens": 8192,  # 符合API要求的范围[1, 8192]
        }

        # 添加重试逻辑
        max_retries = 3
        retry_delay = 5  # 增加重试间隔

        for attempt in range(max_retries):
            try:
                print(f"正在调用API (尝试 {attempt+1}/{max_retries})...")
                response = requests.post(
                    f"{self.base_url}/chat/completions",
                    headers=headers,
                    json=payload,
                    timeout=6000,  # 增加超时时间
                )

                print(f"API响应状态码: {response.status_code}")

                if response.status_code == 200:
                    result = response.json()
                    print("API调用成功")
                    return result
                elif response.status_code == 429:  # 速率限制
                    print(f"API速率限制，等待重试... ({attempt+1}/{max_retries})")
                    time.sleep(retry_delay * (attempt + 1))  # 指数退避
                elif response.status_code == 401:
                    print("API密钥无效，请检查配置")
                    raise ValueError("API密钥无效")
                else:
                    print(f"API错误: {response.status_code}")
                    print(f"响应内容: {response.text[:500]}...")  # 只显示前500字符
                    if attempt == max_retries - 1:  # 最后一次尝试
                        response.raise_for_status()
                    time.sleep(retry_delay)
            except requests.exceptions.Timeout as e:
                print(f"请求超时: {str(e)}")
                if attempt == max_retries - 1:  # 最后一次尝试
                    raise
                time.sleep(retry_delay * (attempt + 1))
            except requests.exceptions.ConnectionError as e:
                print(f"连接错误: {str(e)}")
                if attempt == max_retries - 1:  # 最后一次尝试
                    raise
                time.sleep(retry_delay * (attempt + 1))
            except Exception as e:
                print(f"请求异常: {str(e)}")
                if attempt == max_retries - 1:  # 最后一次尝试
                    raise
                time.sleep(retry_delay)

        return {"error": "所有重试都失败了"}

    def _extract_text_from_response(self, response: Dict[Any, Any]) -> str:
        """
        从API响应中提取文本内容

        Args:
            response: API响应

        Returns:
            文本内容
        """
        try:
            content = (
                response.get("choices", [{}])[0].get("message", {}).get("content", "")
            )
            return content.strip()
        except (KeyError, IndexError) as e:
            print(f"提取文本时出错: {str(e)}")
            return ""
