"""
测试最终梳理后的系统
"""

from pdf2html_workflow import PDF2HTMLWorkflowClean


def test_final_system():
    """测试最终梳理后的系统"""
    pdf_path = "data_1.pdf"
    output_file = "test_final_system.html"

    print("🧪 测试最终梳理后的PDF转HTML系统")
    print("=" * 60)
    print("系统架构:")
    print("1. PDFProcessor - PDF页面提取和图像转换")
    print("2. VLMContentRecognizer - 统一内容识别（一次API调用完成所有工作）")
    print("3. HTMLGenerator - HTML生成和内容合并")
    print("4. PDF2HTMLWorkflowClean - 简化的LangGraph工作流")
    print("=" * 60)

    try:
        # 创建工作流
        workflow = PDF2HTMLWorkflowClean(pdf_path, output_file)

        # 只处理第一页进行测试
        result = workflow.run(page_range=(0, 1))

        if result["success"]:
            print(f"✅ 测试成功!")
            print(f"📄 处理页数: {result['processed_pages']}/{result['total_pages']}")
            print(f"📁 输出文件: {result['output_file']}")

            # 显示文件内容分析
            with open(output_file, "r", encoding="utf-8") as f:
                content = f.read()
                print(f"📊 文件大小: {len(content):,} 字符")

                # 检查是否包含各种内容类型
                has_table = "<table" in content
                has_paragraph = "<p>" in content or "<p " in content
                has_heading = any(f"<h{i}" in content for i in range(1, 7))
                has_list = "<ul>" in content or "<ol>" in content

                print(f"📋 内容分析:")
                print(f"  - 包含表格: {'是' if has_table else '否'}")
                print(f"  - 包含段落: {'是' if has_paragraph else '否'}")
                print(f"  - 包含标题: {'是' if has_heading else '否'}")
                print(f"  - 包含列表: {'是' if has_list else '否'}")

                # 显示内容预览
                print(f"\n📋 内容预览（前500字符）:")
                print("-" * 50)
                preview = content[content.find('<div class="page-marker">') :]
                preview = preview[preview.find("-->") + 3 :].strip()
                print(preview[:500] + "..." if len(preview) > 500 else preview)
                print("-" * 50)

        else:
            print(f"❌ 测试失败!")
            print(f"错误信息: {result['error_message']}")

    except Exception as e:
        print(f"❌ 测试异常: {str(e)}")
        import traceback

        traceback.print_exc()

    print("=" * 60)


def test_multiple_pages():
    """测试多页处理"""
    pdf_path = "data_1.pdf"
    output_file = "test_multiple_pages.html"

    print("\n🧪 测试多页处理")
    print("=" * 60)

    try:
        # 创建工作流
        workflow = PDF2HTMLWorkflowClean(pdf_path, output_file)

        # 处理前3页
        result = workflow.run(page_range=(0, 3))

        if result["success"]:
            print(f"✅ 多页测试成功!")
            print(f"📄 处理页数: {result['processed_pages']}/{result['total_pages']}")
            print(f"📁 输出文件: {result['output_file']}")

        else:
            print(f"❌ 多页测试失败!")
            print(f"错误信息: {result['error_message']}")

    except Exception as e:
        print(f"❌ 多页测试异常: {str(e)}")

    print("=" * 60)


if __name__ == "__main__":
    # 测试单页
    # test_final_system()

    # 测试多页
    test_multiple_pages()
