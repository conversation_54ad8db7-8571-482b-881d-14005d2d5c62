"""
PDF解析Agent - 专门负责将PDF页面转换为HTML
"""

import re
from typing import Dict, Any, <PERSON><PERSON>
from dataclasses import dataclass

from vlm_agent_base import VLMAgentBase
from config import PARSE_PDF_PROMPT


@dataclass
class PDFParseResult:
    """PDF解析结果"""
    content_type: str  # 内容类型：纯文本/包含表格/混合内容
    html_content: str  # HTML内容
    raw_response: str  # VLM原始响应


class PDFParseAgent(VLMAgentBase):
    """PDF解析Agent - 负责将PDF页面转换为HTML"""

    def __init__(self, **kwargs):
        """初始化PDF解析Agent"""
        super().__init__(agent_name="PDF解析Agent", **kwargs)

    def get_prompt(self) -> str:
        """获取PDF解析提示词"""
        return PARSE_PDF_PROMPT

    def parse_response(self, response_text: str) -> PDFParseResult:
        """
        解析VLM响应，提取内容类型和HTML内容

        Args:
            response_text: VLM原始响应

        Returns:
            PDFParseResult: 解析结果
        """
        # 保存原始响应用于调试
        raw_response = response_text.strip()

        # 初始化默认值
        content_type = "混合内容"
        html_content = ""

        try:
            # 提取内容类型
            content_type_match = re.search(
                r"内容类型[：:]\s*\[?([^\]]+)\]?", response_text, re.IGNORECASE
            )
            if content_type_match:
                content_type = content_type_match.group(1).strip()

            # 提取HTML内容
            html_match = re.search(
                r"HTML[：:]\s*\n(.*)", response_text, re.DOTALL | re.IGNORECASE
            )
            if html_match:
                html_content = html_match.group(1).strip()
            else:
                # 如果没有找到HTML标记，尝试提取代码块
                code_block_match = re.search(
                    r"```html\s*\n(.*?)\n```", response_text, re.DOTALL
                )
                if code_block_match:
                    html_content = code_block_match.group(1).strip()
                else:
                    # 如果都没有找到，使用整个响应作为HTML内容
                    html_content = response_text.strip()

            # 清理HTML内容
            html_content = self._clean_html_content(html_content)

            print(f"[{self.agent_name}] 解析结果:")
            print(f"  内容类型: {content_type}")
            print(f"  HTML长度: {len(html_content)} 字符")

            return PDFParseResult(
                content_type=content_type,
                html_content=html_content,
                raw_response=raw_response,
            )

        except Exception as e:
            print(f"[{self.agent_name}] 解析响应时出错: {str(e)}")
            # 返回默认结果
            return PDFParseResult(
                content_type="解析错误",
                html_content="",
                raw_response=raw_response,
            )

    def _clean_html_content(self, html_content: str) -> str:
        """
        清理HTML内容

        Args:
            html_content: 原始HTML内容

        Returns:
            str: 清理后的HTML内容
        """
        if not html_content:
            return ""

        # 移除可能的代码块标记
        html_content = re.sub(r"```html\s*\n?", "", html_content)
        html_content = re.sub(r"\n?```\s*$", "", html_content)

        # 移除不需要的HTML标签
        unwanted_tags = ["<html>", "</html>", "<head>", "</head>", "<body>", "</body>"]
        for tag in unwanted_tags:
            html_content = html_content.replace(tag, "")

        # 清理多余的空白
        html_content = re.sub(r"\n\s*\n", "\n", html_content)
        html_content = html_content.strip()

        return html_content

    def validate_html_content(self, html_content: str) -> bool:
        """
        验证HTML内容的有效性

        Args:
            html_content: HTML内容

        Returns:
            bool: 是否有效
        """
        if not html_content or html_content.strip() == "":
            return False

        # 检查是否包含基本的HTML标签
        html_tags = ["<p>", "<table>", "<h1>", "<h2>", "<h3>", "<h4>", "<h5>", "<h6>", "<div>", "<span>"]
        has_html_tags = any(tag in html_content.lower() for tag in html_tags)

        return has_html_tags

    def get_content_statistics(self, html_content: str) -> Dict[str, int]:
        """
        获取HTML内容统计信息

        Args:
            html_content: HTML内容

        Returns:
            Dict[str, int]: 统计信息
        """
        if not html_content:
            return {"total_length": 0, "table_count": 0, "paragraph_count": 0}

        stats = {
            "total_length": len(html_content),
            "table_count": len(re.findall(r"<table", html_content, re.IGNORECASE)),
            "paragraph_count": len(re.findall(r"<p", html_content, re.IGNORECASE)),
            "heading_count": len(re.findall(r"<h[1-6]", html_content, re.IGNORECASE)),
        }

        return stats
