"""
PDF处理模块 - 负责PDF页面提取和图像转换
"""

import os
import io
from typing import List, Tuple, Dict, Any, Optional
import base64

from PIL import Image
from pypdf import PdfReader
import tempfile

# 尝试导入PyMuPDF，如果不可用则使用替代方案
try:
    import fitz  # PyMuPDF

    PYMUPDF_AVAILABLE = True
except ImportError:
    PYMUPDF_AVAILABLE = False
    try:
        from pdf2image import convert_from_path

        PDF2IMAGE_AVAILABLE = True
    except ImportError:
        PDF2IMAGE_AVAILABLE = False

from config import PDF_DPI, IMAGE_FORMAT


class PDFProcessor:
    """PDF处理器类，负责PDF页面提取和图像转换"""

    def __init__(self, pdf_path: str, dpi: int = PDF_DPI):
        """
        初始化PDF处理器

        Args:
            pdf_path: PDF文件路径
            dpi: 图像DPI
        """
        self.pdf_path = pdf_path
        self.dpi = dpi
        self._validate_pdf()

    def _validate_pdf(self) -> None:
        """验证PDF文件是否存在且可读"""
        if not os.path.exists(self.pdf_path):
            raise FileNotFoundError(f"PDF文件不存在: {self.pdf_path}")

        try:
            with open(self.pdf_path, "rb") as f:
                PdfReader(f)
        except Exception as e:
            raise ValueError(f"无法读取PDF文件: {str(e)}")

    def get_page_count(self) -> int:
        """获取PDF页数"""
        with open(self.pdf_path, "rb") as f:
            pdf = PdfReader(f)
            return len(pdf.pages)

    def convert_page_to_image(
        self, page_num: int, optimize_size: bool = True
    ) -> Image.Image:
        """
        将指定页面转换为PIL图像

        Args:
            page_num: 页码（从0开始）
            optimize_size: 是否优化图像大小

        Returns:
            PIL图像对象
        """
        print(f"正在转换第 {page_num + 1} 页为图像...")

        if PYMUPDF_AVAILABLE:
            # 使用PyMuPDF转换
            doc = fitz.open(self.pdf_path)
            page = doc.load_page(page_num)
            pix = page.get_pixmap(dpi=self.dpi)
            img = Image.frombytes("RGB", [pix.width, pix.height], pix.samples)
            doc.close()
        elif PDF2IMAGE_AVAILABLE:
            # 使用pdf2image转换
            images = convert_from_path(
                self.pdf_path,
                dpi=self.dpi,
                first_page=page_num + 1,
                last_page=page_num + 1,
            )
            img = images[0]
        else:
            # 如果两个库都不可用，使用备用方法
            img = self._fallback_convert_page(page_num)

        # 优化图像大小
        if optimize_size:
            img = self._optimize_image_size(img)

        return img

    def _optimize_image_size(
        self, img: Image.Image, max_size_mb: float = 5.0
    ) -> Image.Image:
        """
        优化图像大小，确保不超过指定大小

        Args:
            img: 原始图像
            max_size_mb: 最大大小（MB）

        Returns:
            优化后的图像
        """
        # 获取当前图像大小
        buffer = io.BytesIO()
        img.save(buffer, format="PNG")
        current_size_mb = len(buffer.getvalue()) / (1024 * 1024)

        print(f"原始图像大小: {current_size_mb:.2f} MB")

        # 如果已经小于最大大小，直接返回
        if current_size_mb <= max_size_mb:
            return img

        # 计算需要的压缩比例
        scale_factor = (max_size_mb / current_size_mb) ** 0.5

        # 计算新的尺寸
        new_width = int(img.width * scale_factor)
        new_height = int(img.height * scale_factor)

        # 确保尺寸不会太小
        min_dimension = 1000
        if new_width < min_dimension or new_height < min_dimension:
            scale = min_dimension / min(new_width, new_height)
            new_width = int(new_width * scale)
            new_height = int(new_height * scale)

        print(f"调整图像尺寸: {img.width}x{img.height} -> {new_width}x{new_height}")

        # 调整图像大小
        resized_img = img.resize((new_width, new_height), Image.LANCZOS)

        # 检查新的大小
        buffer = io.BytesIO()
        resized_img.save(buffer, format="PNG")
        new_size_mb = len(buffer.getvalue()) / (1024 * 1024)
        print(f"优化后图像大小: {new_size_mb:.2f} MB")

        return resized_img

    def _fallback_convert_page(self, page_num: int) -> Image.Image:
        """
        备用的页面转换方法（使用pypdf和PIL）
        注意：这种方法质量较低，仅作为备用

        Args:
            page_num: 页码（从0开始）

        Returns:
            PIL图像对象
        """
        # 这是一个非常基础的备用方法，质量不高
        # 实际应用中应该安装PyMuPDF或pdf2image
        from reportlab.pdfgen import canvas
        from reportlab.lib.pagesizes import letter
        from reportlab.graphics import renderPM
        from reportlab.platypus import SimpleDocTemplate

        with open(self.pdf_path, "rb") as f:
            pdf = PdfReader(f)
            if page_num >= len(pdf.pages):
                raise ValueError(f"页码超出范围: {page_num}, 总页数: {len(pdf.pages)}")

            # 创建一个临时文件
            with tempfile.NamedTemporaryFile(suffix=".png", delete=False) as tmp:
                tmp_path = tmp.name

            # 使用reportlab渲染页面
            # 注意：这种方法不能完美还原PDF，只是一个备用方案
            width, height = letter
            c = canvas.Canvas(tmp_path, pagesize=letter)
            c.drawString(100, 100, f"PDF页面 {page_num+1} (备用渲染)")
            c.save()

            # 打开渲染的图像
            img = Image.open(tmp_path)
            os.unlink(tmp_path)  # 删除临时文件
            return img

    def get_all_pages_as_images(self) -> List[Image.Image]:
        """
        将所有页面转换为图像列表

        Returns:
            图像列表
        """
        page_count = self.get_page_count()
        return [self.convert_page_to_image(i) for i in range(page_count)]

    def image_to_base64(self, image: Image.Image, format: str = IMAGE_FORMAT) -> str:
        """
        将PIL图像转换为base64编码

        Args:
            image: PIL图像对象
            format: 图像格式

        Returns:
            base64编码的图像字符串
        """
        buffered = io.BytesIO()
        image.save(buffered, format=format)
        return base64.b64encode(buffered.getvalue()).decode("utf-8")
