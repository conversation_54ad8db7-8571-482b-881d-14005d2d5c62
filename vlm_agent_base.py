"""
VLM Agent工具 - 基于LangGraph的VLM工具和Agent架构
"""

import os
import json
import time
import io
import base64
from typing import Dict, Any, Optional, Tuple, List, Annotated
from dataclasses import dataclass
from datetime import datetime

import requests
from PIL import Image
from langchain_core.tools import tool
from langchain_core.messages import BaseMessage, HumanMessage
from langchain_core.language_models.chat_models import BaseChatModel
from langgraph.prebuilt import create_react_agent
from langgraph.graph import StateGraph, END

from config import (
    QWEN_VL_API_KEY,
    QWEN_VL_BASE_URL,
    QWEN_VL_MODEL,
    PARSE_PDF_PROMPT,
    TABLE_JUDGE_PROMPT,
    AGENT_TIMEOUT,
    AGENT_MAX_RETRIES,
    AGENT_RETRY_DELAY,
    ENABLE_PARALLEL_AGENTS,
)


@dataclass
class AgentResult:
    """Agent执行结果"""

    agent_name: str
    success: bool
    result: Any
    error_message: str = ""
    execution_time: float = 0.0
    retry_count: int = 0


class VLMAgentBase(ABC):
    """VLM Agent基类"""

    def __init__(
        self,
        agent_name: str,
        api_key: str = QWEN_VL_API_KEY,
        base_url: str = QWEN_VL_BASE_URL,
        model: str = QWEN_VL_MODEL,
        timeout: int = AGENT_TIMEOUT,
        max_retries: int = AGENT_MAX_RETRIES,
        retry_delay: int = AGENT_RETRY_DELAY,
    ):
        """
        初始化VLM Agent

        Args:
            agent_name: Agent名称
            api_key: API密钥
            base_url: API基础URL
            model: 模型名称
            timeout: 超时时间
            max_retries: 最大重试次数
            retry_delay: 重试延迟
        """
        self.agent_name = agent_name
        self.api_key = api_key
        self.base_url = base_url
        self.model = model
        self.timeout = timeout
        self.max_retries = max_retries
        self.retry_delay = retry_delay
        self._validate_credentials()

    def _validate_credentials(self) -> None:
        """验证API凭证是否有效"""
        if not self.api_key or not self.base_url:
            raise ValueError(f"[{self.agent_name}] API密钥和基础URL不能为空")

    @abstractmethod
    def get_prompt(self) -> str:
        """获取Agent的提示词"""
        pass

    @abstractmethod
    def parse_response(self, response_text: str) -> Any:
        """解析VLM响应"""
        pass

    def _prepare_image_for_api(self, image: Image.Image) -> str:
        """准备图像用于API调用"""
        import io
        import base64

        # 转换为RGB格式（如果需要）
        if image.mode != "RGB":
            image = image.convert("RGB")

        # 压缩图像以减少API调用大小
        max_size = (1024, 1024)
        if image.size[0] > max_size[0] or image.size[1] > max_size[1]:
            image.thumbnail(max_size, Image.Resampling.LANCZOS)

        # 转换为base64
        buffer = io.BytesIO()
        image.save(buffer, format="JPEG", quality=85)
        image_base64 = base64.b64encode(buffer.getvalue()).decode("utf-8")

        return f"data:image/jpeg;base64,{image_base64}"

    def _call_vlm_api(self, image: Image.Image) -> str:
        """调用VLM API"""
        image_data = self._prepare_image_for_api(image)
        prompt = self.get_prompt()

        headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json",
        }

        payload = {
            "model": self.model,
            "messages": [
                {
                    "role": "user",
                    "content": [
                        {"type": "text", "text": prompt},
                        {"type": "image_url", "image_url": {"url": image_data}},
                    ],
                }
            ],
            "max_tokens": 4000,
            "temperature": 0.1,
        }

        response = requests.post(
            f"{self.base_url}/chat/completions",
            headers=headers,
            json=payload,
            timeout=self.timeout,
        )

        if response.status_code != 200:
            raise Exception(f"API调用失败: {response.status_code}, {response.text}")

        response_data = response.json()
        if "choices" not in response_data or not response_data["choices"]:
            raise Exception("API响应格式错误")

        return response_data["choices"][0]["message"]["content"]

    def execute(self, image: Image.Image, page_num: int) -> AgentResult:
        """执行Agent任务"""
        start_time = time.time()
        retry_count = 0

        for attempt in range(self.max_retries):
            try:
                print(
                    f"[{self.agent_name}] 执行第 {page_num} 页分析... (尝试 {attempt+1}/{self.max_retries})"
                )

                # 调用VLM API
                response_text = self._call_vlm_api(image)

                # 解析响应
                result = self.parse_response(response_text)

                execution_time = time.time() - start_time
                print(f"[{self.agent_name}] 执行成功，耗时 {execution_time:.2f}s")

                return AgentResult(
                    agent_name=self.agent_name,
                    success=True,
                    result=result,
                    execution_time=execution_time,
                    retry_count=retry_count,
                )

            except Exception as e:
                retry_count += 1
                error_msg = f"[{self.agent_name}] 执行失败: {str(e)}"
                print(error_msg)

                if attempt < self.max_retries - 1:
                    print(f"[{self.agent_name}] 等待 {self.retry_delay} 秒后重试...")
                    time.sleep(self.retry_delay)
                    self.retry_delay *= 2  # 指数退避
                else:
                    execution_time = time.time() - start_time
                    return AgentResult(
                        agent_name=self.agent_name,
                        success=False,
                        result=None,
                        error_message=error_msg,
                        execution_time=execution_time,
                        retry_count=retry_count,
                    )

        # 不应该到达这里
        execution_time = time.time() - start_time
        return AgentResult(
            agent_name=self.agent_name,
            success=False,
            result=None,
            error_message=f"[{self.agent_name}] 未知错误",
            execution_time=execution_time,
            retry_count=retry_count,
        )


class VLMAgentCoordinator:
    """VLM Agent协调器 - 负责并行执行多个Agent并汇总结果"""

    def __init__(self, agents: List[VLMAgentBase]):
        """
        初始化协调器

        Args:
            agents: Agent列表
        """
        self.agents = agents

    def execute_parallel(
        self, image: Image.Image, page_num: int
    ) -> Dict[str, AgentResult]:
        """并行执行所有Agent"""
        if not ENABLE_PARALLEL_AGENTS or len(self.agents) <= 1:
            return self._execute_sequential(image, page_num)

        print(f"并行执行 {len(self.agents)} 个Agent...")
        start_time = time.time()
        results = {}

        with ThreadPoolExecutor(max_workers=len(self.agents)) as executor:
            # 提交所有任务
            future_to_agent = {
                executor.submit(agent.execute, image, page_num): agent
                for agent in self.agents
            }

            # 收集结果
            for future in as_completed(future_to_agent):
                agent = future_to_agent[future]
                try:
                    result = future.result()
                    results[agent.agent_name] = result
                except Exception as e:
                    results[agent.agent_name] = AgentResult(
                        agent_name=agent.agent_name,
                        success=False,
                        result=None,
                        error_message=f"并行执行异常: {str(e)}",
                    )

        total_time = time.time() - start_time
        print(f"并行执行完成，总耗时 {total_time:.2f}s")
        return results

    def _execute_sequential(
        self, image: Image.Image, page_num: int
    ) -> Dict[str, AgentResult]:
        """顺序执行所有Agent"""
        print(f"顺序执行 {len(self.agents)} 个Agent...")
        results = {}

        for agent in self.agents:
            result = agent.execute(image, page_num)
            results[agent.agent_name] = result

        return results
