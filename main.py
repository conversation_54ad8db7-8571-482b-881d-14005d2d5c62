"""
PDF转HTML主程序 - 重新梳理后的简化版本
使用LangGraph和VLM实现PDF内容识别和HTML转换
"""

import os
import sys
import argparse
from typing import Optional

from pdf2html_workflow import PDF2HTMLWorkflowClean


def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description="PDF转HTML工具 - 完整识别PDF内容（文本、表格）并转换为HTML"
    )
    parser.add_argument("pdf_path", help="PDF文件路径")
    parser.add_argument("-o", "--output", default="output.html", help="输出HTML文件路径")
    parser.add_argument("-v", "--verbose", action="store_true", help="详细输出")
    parser.add_argument("-p", "--pages", help="要处理的页面范围，格式为'start-end'，例如'1-3'表示处理第1到第3页")
    parser.add_argument("-s", "--single", type=int, help="只处理指定的单页，例如'2'表示只处理第2页")
    parser.add_argument("--dpi", type=int, default=200, help="PDF转图像的DPI，默认为200")
    parser.add_argument("--max-size", type=float, default=5.0, help="图像最大大小(MB)，默认为5MB")
    
    args = parser.parse_args()
    
    # 验证输入文件
    if not os.path.exists(args.pdf_path):
        print(f"错误: PDF文件不存在: {args.pdf_path}")
        sys.exit(1)
    
    if not args.pdf_path.lower().endswith('.pdf'):
        print(f"错误: 输入文件不是PDF格式: {args.pdf_path}")
        sys.exit(1)
    
    # 处理页面范围
    page_range = None
    if args.single is not None:
        if args.single < 1:
            print(f"错误: 页码必须大于0: {args.single}")
            sys.exit(1)
        # 对于单页处理，起始页和结束页相同
        page_range = (args.single - 1, args.single)  # 转换为0-based索引
        print(f"⚠️ 只处理第 {args.single} 页")
    elif args.pages:
        try:
            start, end = map(int, args.pages.split('-'))
            if start < 1 or end < start:
                print(f"错误: 无效的页面范围: {args.pages}")
                sys.exit(1)
            # 结束页需要加1，因为我们的范围是[start, end)
            page_range = (start - 1, end)  # 转换为0-based索引
            print(f"⚠️ 处理第 {start} 到第 {end} 页")
        except ValueError:
            print(f"错误: 无效的页面范围格式: {args.pages}，应为'start-end'")
            sys.exit(1)
    
    try:
        # 创建工作流
        workflow = PDF2HTMLWorkflowClean(args.pdf_path, args.output)
        
        # 运行转换
        result = workflow.run(page_range=page_range)
        
        if result["success"]:
            print(f"\n✅ 转换成功!")
            print(f"📄 处理页数: {result['processed_pages']}/{result['total_pages']}")
            print(f"📁 输出文件: {os.path.abspath(result['output_file'])}")
            
            # 显示文件大小
            if os.path.exists(result['output_file']):
                file_size = os.path.getsize(result['output_file'])
                print(f"📊 文件大小: {file_size:,} 字节")
        else:
            print(f"\n❌ 转换失败!")
            print(f"错误信息: {result['error_message']}")
            sys.exit(1)
            
    except KeyboardInterrupt:
        print("\n⚠️ 用户中断操作")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 程序异常: {str(e)}")
        import traceback
        traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    print("🚀 PDF转HTML工具 - 重新梳理后的简化版本")
    print("=" * 60)
    print("功能：完整识别PDF内容（文本、表格）并转换为HTML")
    print("优化：使用统一的VLM提示词，一次API调用完成所有工作")
    print("=" * 60)
    main()
