"""
VLM Agents - 基于LangGraph的并行VLM Agent架构
使用LangGraph的Send API实现两个VLM Agent的并行执行
"""

import time
import io
import base64
import re
from typing import Dict, Any, Optional, List, TypedDict

import requests
from PIL import Image

from langgraph.graph import StateGraph, START, END

from config import (
    QWEN_VL_API_KEY,
    QWEN_VL_BASE_URL,
    QWEN_VL_MODEL,
    PARSE_PDF_PROMPT,
    TABLE_JUDGE_PROMPT,
    AGENT_TIMEOUT,
    AGENT_MAX_RETRIES,
    AGENT_RETRY_DELAY,
)


class AgentState(TypedDict):
    """Agent状态定义"""

    page_image: Optional[Any]  # PIL Image
    page_num: int

    # PDF解析Agent结果
    parse_success: bool
    content_type: str
    html_content: str
    parse_error: str
    parse_raw_response: str

    # 续表判断Agent结果
    judge_success: bool
    is_continuation: bool
    judge_error: str
    judge_raw_response: str

    # 汇总结果
    final_success: bool
    final_html: str
    final_error: str


class VLMService:
    """VLM服务类 - 提供基础的VLM API调用功能"""

    def __init__(
        self,
        api_key: str = QWEN_VL_API_KEY,
        base_url: str = QWEN_VL_BASE_URL,
        model: str = QWEN_VL_MODEL,
        timeout: int = AGENT_TIMEOUT,
        max_retries: int = AGENT_MAX_RETRIES,
        retry_delay: int = AGENT_RETRY_DELAY,
    ):
        """初始化VLM服务"""
        self.api_key = api_key
        self.base_url = base_url
        self.model = model
        self.timeout = timeout
        self.max_retries = max_retries
        self.retry_delay = retry_delay
        self._validate_credentials()

    def _validate_credentials(self) -> None:
        """验证API凭证是否有效"""
        if not self.api_key or not self.base_url:
            raise ValueError("API密钥和基础URL不能为空")

    def _prepare_image_for_api(self, image: Image.Image) -> str:
        """准备图像用于API调用"""
        # 转换为RGB格式（如果需要）
        if image.mode != "RGB":
            image = image.convert("RGB")

        # 压缩图像以减少API调用大小
        max_size = (1024, 1024)
        if image.size[0] > max_size[0] or image.size[1] > max_size[1]:
            image.thumbnail(max_size, Image.Resampling.LANCZOS)

        # 转换为base64
        buffer = io.BytesIO()
        image.save(buffer, format="JPEG", quality=85)
        image_base64 = base64.b64encode(buffer.getvalue()).decode("utf-8")

        return f"data:image/jpeg;base64,{image_base64}"

    def call_vlm_api(self, image: Image.Image, prompt: str) -> str:
        """调用VLM API"""
        image_data = self._prepare_image_for_api(image)

        headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json",
        }

        payload = {
            "model": self.model,
            "messages": [
                {
                    "role": "user",
                    "content": [
                        {"type": "text", "text": prompt},
                        {"type": "image_url", "image_url": {"url": image_data}},
                    ],
                }
            ],
            "max_tokens": 4000,
            "temperature": 0.1,
        }

        response = requests.post(
            f"{self.base_url}/chat/completions",
            headers=headers,
            json=payload,
            timeout=self.timeout,
        )

        if response.status_code != 200:
            raise Exception(f"API调用失败: {response.status_code}, {response.text}")

        response_data = response.json()
        if "choices" not in response_data or not response_data["choices"]:
            raise Exception("API响应格式错误")

        return response_data["choices"][0]["message"]["content"]

    def execute_with_retry(
        self, image: Image.Image, prompt: str, task_name: str
    ) -> Dict[str, Any]:
        """带重试的执行VLM任务"""
        start_time = time.time()
        retry_delay = self.retry_delay

        for attempt in range(self.max_retries):
            try:
                print(f"[{task_name}] 执行中... (尝试 {attempt+1}/{self.max_retries})")

                # 调用VLM API
                response_text = self.call_vlm_api(image, prompt)

                execution_time = time.time() - start_time
                print(f"[{task_name}] 执行成功，耗时 {execution_time:.2f}s")

                return {
                    "success": True,
                    "result": response_text,
                    "error": "",
                    "execution_time": execution_time,
                }

            except Exception as e:
                error_msg = f"[{task_name}] 执行失败: {str(e)}"
                print(error_msg)

                if attempt < self.max_retries - 1:
                    print(f"[{task_name}] 等待 {retry_delay} 秒后重试...")
                    time.sleep(retry_delay)
                    retry_delay *= 2  # 指数退避
                else:
                    execution_time = time.time() - start_time
                    return {
                        "success": False,
                        "result": "",
                        "error": error_msg,
                        "execution_time": execution_time,
                    }

        # 不应该到达这里
        execution_time = time.time() - start_time
        return {
            "success": False,
            "result": "",
            "error": f"[{task_name}] 未知错误",
            "execution_time": execution_time,
        }


# 全局VLM服务实例
vlm_service = VLMService()


def pdf_parse_agent(state: AgentState) -> dict:
    """PDF解析Agent - 负责将PDF页面转换为HTML"""
    print(f"[PDF解析Agent] 开始处理第 {state['page_num']} 页...")

    page_image = state["page_image"]
    if not page_image:
        return {
            "parse_success": False,
            "content_type": "解析错误",
            "html_content": "",
            "parse_error": "页面图像为空",
            "parse_raw_response": "",
        }

    # 调用VLM服务
    result = vlm_service.execute_with_retry(
        page_image, PARSE_PDF_PROMPT, "PDF解析Agent"
    )

    if not result["success"]:
        return {
            "parse_success": False,
            "content_type": "解析错误",
            "html_content": "",
            "parse_error": result["error"],
            "parse_raw_response": "",
        }

    # 解析响应
    response_text = result["result"]
    content_type = "混合内容"
    html_content = ""

    try:
        # 提取内容类型
        content_type_match = re.search(
            r"内容类型[：:]\s*\[?([^\]]+)\]?", response_text, re.IGNORECASE
        )
        if content_type_match:
            content_type = content_type_match.group(1).strip()

        # 提取HTML内容
        html_match = re.search(
            r"HTML[：:]\s*\n(.*)", response_text, re.DOTALL | re.IGNORECASE
        )
        if html_match:
            html_content = html_match.group(1).strip()
        else:
            # 如果没有找到HTML标记，尝试提取代码块
            code_block_match = re.search(
                r"```html\s*\n(.*?)\n```", response_text, re.DOTALL
            )
            if code_block_match:
                html_content = code_block_match.group(1).strip()
            else:
                # 如果都没有找到，使用整个响应作为HTML内容
                html_content = response_text.strip()

        # 清理HTML内容
        html_content = _clean_html_content(html_content)

        print(
            f"[PDF解析Agent] 解析完成 - 内容类型: {content_type}, HTML长度: {len(html_content)}"
        )

        return {
            "parse_success": True,
            "content_type": content_type,
            "html_content": html_content,
            "parse_error": "",
            "parse_raw_response": response_text,
        }

    except Exception as e:
        error_msg = f"解析响应时出错: {str(e)}"
        print(f"[PDF解析Agent] {error_msg}")
        return {
            "parse_success": False,
            "content_type": "解析错误",
            "html_content": "",
            "parse_error": error_msg,
            "parse_raw_response": response_text,
        }


def table_judge_agent(state: AgentState) -> dict:
    """续表判断Agent - 负责判断页面顶部表格是否为续表"""
    print(f"[续表判断Agent] 开始处理第 {state['page_num']} 页...")

    page_image = state["page_image"]
    if not page_image:
        return {
            "judge_success": False,
            "is_continuation": False,
            "judge_error": "页面图像为空",
            "judge_raw_response": "",
        }

    # 调用VLM服务
    result = vlm_service.execute_with_retry(
        page_image, TABLE_JUDGE_PROMPT, "续表判断Agent"
    )

    if not result["success"]:
        return {
            "judge_success": False,
            "is_continuation": False,
            "judge_error": result["error"],
            "judge_raw_response": "",
        }

    # 解析响应
    response_text = result["result"]
    is_continuation = False

    try:
        # 提取续表判断结果
        continuation_match = re.search(
            r"是否续表[：:]\s*\[?([^\]]+)\]?", response_text, re.IGNORECASE
        )
        if continuation_match:
            continuation_result = continuation_match.group(1).strip()
            is_continuation = continuation_result == "是"

        print(
            f"[续表判断Agent] 判断完成 - 是否续表: {'是' if is_continuation else '否'}"
        )

        return {
            "judge_success": True,
            "is_continuation": is_continuation,
            "judge_error": "",
            "judge_raw_response": response_text,
        }

    except Exception as e:
        error_msg = f"解析响应时出错: {str(e)}"
        print(f"[续表判断Agent] {error_msg}")
        return {
            "judge_success": False,
            "is_continuation": False,
            "judge_error": error_msg,
            "judge_raw_response": response_text,
        }


def _clean_html_content(html_content: str) -> str:
    """清理HTML内容"""
    if not html_content:
        return ""

    # 移除可能的代码块标记
    html_content = re.sub(r"```html\s*\n?", "", html_content)
    html_content = re.sub(r"\n?```\s*$", "", html_content)

    # 移除不需要的HTML标签
    unwanted_tags = ["<html>", "</html>", "<head>", "</head>", "<body>", "</body>"]
    for tag in unwanted_tags:
        html_content = html_content.replace(tag, "")

    # 清理多余的空白
    html_content = re.sub(r"\n\s*\n", "\n", html_content)
    html_content = html_content.strip()

    return html_content


def aggregate_results(state: AgentState) -> dict:
    """汇总两个Agent的结果"""
    print(f"[结果汇总] 汇总第 {state['page_num']} 页的Agent结果...")

    parse_success = state.get("parse_success", False)
    judge_success = state.get("judge_success", False)

    # 检查两个Agent是否都成功执行
    if not parse_success:
        error_msg = f"PDF解析Agent失败: {state.get('parse_error', '未知错误')}"
        print(f"[结果汇总] {error_msg}")
        return {
            **state,
            "final_success": False,
            "final_html": "",
            "final_error": error_msg,
        }

    if not judge_success:
        # 续表判断失败时，仍然可以使用解析结果，但设置续表为False
        print(
            f"[结果汇总] 续表判断Agent失败，使用默认值: {state.get('judge_error', '未知错误')}"
        )
        is_continuation = False
    else:
        is_continuation = state.get("is_continuation", False)

    # 获取解析结果
    content_type = state.get("content_type", "混合内容")
    html_content = state.get("html_content", "")

    print(f"[结果汇总] 汇总完成:")
    print(f"  内容类型: {content_type}")
    print(f"  是否续表: {'是' if is_continuation else '否'}")
    print(f"  HTML长度: {len(html_content)} 字符")

    return {
        **state,
        "final_success": True,
        "final_html": html_content,
        "final_error": "",
        # 保持续表判断结果
        "is_continuation": is_continuation,
    }


class VLMAgentWorkflow:
    """VLM Agent工作流 - 使用并行执行的简化架构"""

    def __init__(self):
        """初始化工作流"""
        pass

    def process_page(self, page_image: Image.Image, page_num: int) -> Dict[str, Any]:
        """
        处理单个页面 - 并行执行两个Agent

        Args:
            page_image: 页面图像
            page_num: 页码

        Returns:
            Dict: 处理结果
        """
        print(f"开始并行处理第 {page_num} 页...")

        # 初始状态
        initial_state = AgentState(
            page_image=page_image,
            page_num=page_num,
            parse_success=False,
            content_type="",
            html_content="",
            parse_error="",
            parse_raw_response="",
            judge_success=False,
            is_continuation=False,
            judge_error="",
            judge_raw_response="",
            final_success=False,
            final_html="",
            final_error="",
        )

        try:
            # 使用ThreadPoolExecutor并行执行两个Agent
            from concurrent.futures import ThreadPoolExecutor

            with ThreadPoolExecutor(max_workers=2) as executor:
                # 提交两个Agent任务
                future_parse = executor.submit(pdf_parse_agent, initial_state)
                future_judge = executor.submit(table_judge_agent, initial_state)

                # 收集结果
                parse_result = future_parse.result()
                judge_result = future_judge.result()

                # 合并状态
                merged_state = {
                    **initial_state,
                    **parse_result,
                    **judge_result,
                }

                # 汇总结果
                final_state = aggregate_results(merged_state)

            return {
                "success": final_state.get("final_success", False),
                "content_type": final_state.get("content_type", ""),
                "html_content": final_state.get("final_html", ""),
                "is_continuation": final_state.get("is_continuation", False),
                "error_message": final_state.get("final_error", ""),
                "parse_raw_response": final_state.get("parse_raw_response", ""),
                "judge_raw_response": final_state.get("judge_raw_response", ""),
            }

        except Exception as e:
            error_msg = f"并行Agent执行出错: {str(e)}"
            print(error_msg)
            import traceback

            traceback.print_exc()

            return {
                "success": False,
                "content_type": "执行错误",
                "html_content": "",
                "is_continuation": False,
                "error_message": error_msg,
                "parse_raw_response": "",
                "judge_raw_response": "",
            }
