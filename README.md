# PDF转HTML工具

基于LangGraph和VLM的PDF内容识别与HTML转换系统，能够完整还原PDF中的文本、表格等内容结构。

## 功能特点

✅ **主要功能：**
- 完整识别PDF页面内容（文本、表格等）
- 保留原始文档的结构和布局
- 智能处理跨页表格（基于表头缺失特征）
- 生成格式良好的HTML文档
- 支持单页和页面范围处理
- 使用通义千问VL-Max模型进行内容识别
- LangGraph工作流编排
- 错误处理和重试机制
- 图像大小优化（避免API调用超时）

## 系统架构

```
PDF文件 → PDF处理器 → 图像转换 → VLM识别 → 内容分析 → HTML生成 → 最终输出
```

### 核心组件

1. **pdf_processor.py** - PDF页面提取和图像转换
2. **vlm_service.py** - VLM内容识别服务
3. **table_analyzer.py** - 表格和跨页内容检测
4. **html_generator.py** - HTML输出和内容合并
5. **pdf2html_workflow.py** - LangGraph工作流编排
6. **main.py** - 主程序入口

## 安装和配置

### 1. 环境准备
```bash
conda activate test_vlm_pdf2html
pip install -r requirements.txt
```

### 2. API配置
在 `config.py` 中配置通义千问VL API：
```python
QWEN_VL_API_KEY = "your-api-key"
QWEN_VL_BASE_URL = "https://dashscope.aliyuncs.com/compatible-mode/v1"
QWEN_VL_MODEL = "qwen-vl-max"
```

## 使用方法

### 基本用法
```bash
# 处理整个PDF文件
python main.py input.pdf -o output.html

# 处理单页
python main.py input.pdf -s 1 -o page1.html

# 处理页面范围
python main.py input.pdf -p 1-3 -o pages1-3.html
```

### 参数说明
- `pdf_path`: PDF文件路径
- `-o, --output`: 输出HTML文件路径（默认：output.html）
- `-s, --single`: 只处理指定的单页
- `-p, --pages`: 处理页面范围，格式为'start-end'
- `--dpi`: PDF转图像的DPI（默认：200）
- `--max-size`: 图像最大大小MB（默认：5MB）
- `-v, --verbose`: 详细输出

### 测试工具
```bash
# 测试API连接
python test_api.py

# 测试单页处理
python test_single_page.py
```

## 工作流程

1. **初始化处理** - 检查PDF文件，初始化各组件
2. **页面提取** - 将PDF页面转换为图像
3. **表格识别** - 使用VLM识别表格内容
4. **结构分析** - 判断是否为跨页表格的续表
5. **内容处理** - 将表格内容添加到HTML生成器
6. **页面循环** - 处理下一页或完成文档
7. **文档生成** - 生成最终HTML文件

## 跨页表格处理

系统通过以下特征识别跨页表格的续表：
- 缺少表头（`<th>`标签）
- 第一行内容为数据而非标题
- 与前一页表格结构相似

当检测到续表时，系统会：
1. 提取表格行内容
2. 合并到前一个表格
3. 保持表格结构完整性

## 测试结果

### 成功案例
- ✅ 单页表格识别：成功识别并转换第1页的财务数据表格
- ✅ HTML生成：生成格式良好的HTML表格
- ✅ 页面范围处理：支持指定页面范围处理

### 已知问题
- ⚠️ API超时：在处理某些页面时可能遇到网络超时
- ⚠️ 大图像处理：较大的PDF页面可能导致API调用缓慢

## 文件结构

```
├── main.py                 # 主程序入口
├── config.py              # 配置文件
├── requirements.txt       # 依赖包列表
├── pdf_processor.py       # PDF处理模块
├── vlm_service.py         # VLM服务模块
├── table_analyzer.py      # 表格分析模块
├── html_generator.py      # HTML生成模块
├── pdf2html_workflow.py   # LangGraph工作流
├── test_api.py           # API测试脚本
├── test_single_page.py   # 单页测试脚本
└── utils.py              # 工具函数（备用）
```

## 示例输出

处理PDF第1页后生成的HTML表格：

```html
<table>
  <tr>
    <td></td>
    <td>本报告期</td>
    <td>上年同期</td>
    <td>本报告期比上年同期增减(%)</td>
  </tr>
  <tr>
    <td>营业收入（元）</td>
    <td>1,256,311,576.03</td>
    <td>1,294,231,998.50</td>
    <td>-2.93%</td>
  </tr>
  <!-- 更多行... -->
</table>
```

## 技术特点

- **模块化设计**：各组件职责清晰，易于维护和扩展
- **错误处理**：完善的重试机制和错误恢复
- **性能优化**：图像大小优化，减少API调用时间
- **灵活配置**：支持多种处理模式和参数调整
- **工作流编排**：使用LangGraph实现清晰的处理流程

## 下一步改进

1. **网络优化**：改进API调用的网络处理
2. **并发处理**：支持多页面并发识别
3. **缓存机制**：避免重复处理相同页面
4. **更多格式**：支持更多输出格式（Word、Markdown等）
5. **UI界面**：开发图形用户界面
