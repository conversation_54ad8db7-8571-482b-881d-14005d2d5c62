"""
VLM工具 - 基于LangGraph的VLM工具定义
"""

import os
import json
import time
import io
import base64
import re
from typing import Dict, Any, Optional, Tuple, List, Annotated
from dataclasses import dataclass

import requests
from PIL import Image
from langchain_core.tools import tool

from config import (
    QWEN_VL_API_KEY,
    QWEN_VL_BASE_URL,
    QWEN_VL_MODEL,
    PARSE_PDF_PROMPT,
    TABLE_JUDGE_PROMPT,
    AGENT_TIMEOUT,
    AGENT_MAX_RETRIES,
    AGENT_RETRY_DELAY,
)


@dataclass
class VLMResult:
    """VLM执行结果"""
    success: bool
    result: Any
    error_message: str = ""
    execution_time: float = 0.0
    raw_response: str = ""


class VLMService:
    """VLM服务类 - 提供基础的VLM API调用功能"""

    def __init__(
        self,
        api_key: str = QWEN_VL_API_KEY,
        base_url: str = QWEN_VL_BASE_URL,
        model: str = QWEN_VL_MODEL,
        timeout: int = AGENT_TIMEOUT,
        max_retries: int = AGENT_MAX_RETRIES,
        retry_delay: int = AGENT_RETRY_DELAY,
    ):
        """初始化VLM服务"""
        self.api_key = api_key
        self.base_url = base_url
        self.model = model
        self.timeout = timeout
        self.max_retries = max_retries
        self.retry_delay = retry_delay
        self._validate_credentials()

    def _validate_credentials(self) -> None:
        """验证API凭证是否有效"""
        if not self.api_key or not self.base_url:
            raise ValueError("API密钥和基础URL不能为空")

    def _prepare_image_for_api(self, image: Image.Image) -> str:
        """准备图像用于API调用"""
        # 转换为RGB格式（如果需要）
        if image.mode != "RGB":
            image = image.convert("RGB")

        # 压缩图像以减少API调用大小
        max_size = (1024, 1024)
        if image.size[0] > max_size[0] or image.size[1] > max_size[1]:
            image.thumbnail(max_size, Image.Resampling.LANCZOS)

        # 转换为base64
        buffer = io.BytesIO()
        image.save(buffer, format="JPEG", quality=85)
        image_base64 = base64.b64encode(buffer.getvalue()).decode("utf-8")

        return f"data:image/jpeg;base64,{image_base64}"

    def call_vlm_api(self, image: Image.Image, prompt: str) -> str:
        """调用VLM API"""
        image_data = self._prepare_image_for_api(image)

        headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json",
        }

        payload = {
            "model": self.model,
            "messages": [
                {
                    "role": "user",
                    "content": [
                        {"type": "text", "text": prompt},
                        {"type": "image_url", "image_url": {"url": image_data}},
                    ],
                }
            ],
            "max_tokens": 4000,
            "temperature": 0.1,
        }

        response = requests.post(
            f"{self.base_url}/chat/completions",
            headers=headers,
            json=payload,
            timeout=self.timeout,
        )

        if response.status_code != 200:
            raise Exception(
                f"API调用失败: {response.status_code}, {response.text}"
            )

        response_data = response.json()
        if "choices" not in response_data or not response_data["choices"]:
            raise Exception("API响应格式错误")

        return response_data["choices"][0]["message"]["content"]

    def execute_with_retry(self, image: Image.Image, prompt: str, task_name: str) -> VLMResult:
        """带重试的执行VLM任务"""
        start_time = time.time()
        retry_delay = self.retry_delay

        for attempt in range(self.max_retries):
            try:
                print(f"[{task_name}] 执行中... (尝试 {attempt+1}/{self.max_retries})")
                
                # 调用VLM API
                response_text = self.call_vlm_api(image, prompt)
                
                execution_time = time.time() - start_time
                print(f"[{task_name}] 执行成功，耗时 {execution_time:.2f}s")
                
                return VLMResult(
                    success=True,
                    result=response_text,
                    execution_time=execution_time,
                    raw_response=response_text,
                )

            except Exception as e:
                error_msg = f"[{task_name}] 执行失败: {str(e)}"
                print(error_msg)

                if attempt < self.max_retries - 1:
                    print(f"[{task_name}] 等待 {retry_delay} 秒后重试...")
                    time.sleep(retry_delay)
                    retry_delay *= 2  # 指数退避
                else:
                    execution_time = time.time() - start_time
                    return VLMResult(
                        success=False,
                        result=None,
                        error_message=error_msg,
                        execution_time=execution_time,
                        raw_response="",
                    )

        # 不应该到达这里
        execution_time = time.time() - start_time
        return VLMResult(
            success=False,
            result=None,
            error_message=f"[{task_name}] 未知错误",
            execution_time=execution_time,
            raw_response="",
        )


# 全局VLM服务实例
vlm_service = VLMService()


@tool
def parse_pdf_page(image_base64: Annotated[str, "Base64编码的PDF页面图像"]) -> Dict[str, Any]:
    """
    解析PDF页面，将其转换为HTML格式
    
    Args:
        image_base64: Base64编码的PDF页面图像
        
    Returns:
        Dict包含解析结果：content_type, html_content, raw_response
    """
    try:
        # 解码base64图像
        image_data = base64.b64decode(image_base64)
        image = Image.open(io.BytesIO(image_data))
        
        # 调用VLM服务
        result = vlm_service.execute_with_retry(image, PARSE_PDF_PROMPT, "PDF解析")
        
        if not result.success:
            return {
                "success": False,
                "content_type": "解析错误",
                "html_content": "",
                "error_message": result.error_message,
                "raw_response": result.raw_response,
            }
        
        # 解析响应
        response_text = result.result
        content_type = "混合内容"
        html_content = ""
        
        # 提取内容类型
        content_type_match = re.search(
            r"内容类型[：:]\s*\[?([^\]]+)\]?", response_text, re.IGNORECASE
        )
        if content_type_match:
            content_type = content_type_match.group(1).strip()

        # 提取HTML内容
        html_match = re.search(
            r"HTML[：:]\s*\n(.*)", response_text, re.DOTALL | re.IGNORECASE
        )
        if html_match:
            html_content = html_match.group(1).strip()
        else:
            # 如果没有找到HTML标记，尝试提取代码块
            code_block_match = re.search(
                r"```html\s*\n(.*?)\n```", response_text, re.DOTALL
            )
            if code_block_match:
                html_content = code_block_match.group(1).strip()
            else:
                # 如果都没有找到，使用整个响应作为HTML内容
                html_content = response_text.strip()

        # 清理HTML内容
        html_content = _clean_html_content(html_content)
        
        return {
            "success": True,
            "content_type": content_type,
            "html_content": html_content,
            "raw_response": response_text,
        }
        
    except Exception as e:
        return {
            "success": False,
            "content_type": "解析错误",
            "html_content": "",
            "error_message": f"PDF解析工具执行失败: {str(e)}",
            "raw_response": "",
        }


@tool
def judge_table_continuation(image_base64: Annotated[str, "Base64编码的PDF页面图像"]) -> Dict[str, Any]:
    """
    判断PDF页面顶部的表格是否为续表
    
    Args:
        image_base64: Base64编码的PDF页面图像
        
    Returns:
        Dict包含判断结果：is_continuation, raw_response
    """
    try:
        # 解码base64图像
        image_data = base64.b64decode(image_base64)
        image = Image.open(io.BytesIO(image_data))
        
        # 调用VLM服务
        result = vlm_service.execute_with_retry(image, TABLE_JUDGE_PROMPT, "续表判断")
        
        if not result.success:
            return {
                "success": False,
                "is_continuation": False,
                "error_message": result.error_message,
                "raw_response": result.raw_response,
            }
        
        # 解析响应
        response_text = result.result
        is_continuation = False
        
        # 提取续表判断结果
        continuation_match = re.search(
            r"是否续表[：:]\s*\[?([^\]]+)\]?", response_text, re.IGNORECASE
        )
        if continuation_match:
            continuation_result = continuation_match.group(1).strip()
            is_continuation = continuation_result == "是"
        
        return {
            "success": True,
            "is_continuation": is_continuation,
            "raw_response": response_text,
        }
        
    except Exception as e:
        return {
            "success": False,
            "is_continuation": False,
            "error_message": f"续表判断工具执行失败: {str(e)}",
            "raw_response": "",
        }


def _clean_html_content(html_content: str) -> str:
    """清理HTML内容"""
    if not html_content:
        return ""

    # 移除可能的代码块标记
    html_content = re.sub(r"```html\s*\n?", "", html_content)
    html_content = re.sub(r"\n?```\s*$", "", html_content)

    # 移除不需要的HTML标签
    unwanted_tags = ["<html>", "</html>", "<head>", "</head>", "<body>", "</body>"]
    for tag in unwanted_tags:
        html_content = html_content.replace(tag, "")

    # 清理多余的空白
    html_content = re.sub(r"\n\s*\n", "\n", html_content)
    html_content = html_content.strip()

    return html_content
